

### 🚀 ** Build the Production-Ready Enterprise AI Platform (Final, Stack-Specified Version)**

> You are a **Principal Technical Architect** and **Lead Software Engineer** responsible for generating a **complete, production-ready, full-stack implementation plan** for an **Enterprise AI Platform**. This is not a prototype or demo. This is a **globally scalable, secure, and real-time system** that will be deployed in production for enterprise customers.
>
> You must generate a **comprehensive, actionable, and executable blueprint** based on the following specifications, architecture, and requirements.

---

### 🔧 **Core System Mandate**

Build a next-generation enterprise AI platform where **AI agents, tools, workflows, and humans** collaborate in **real time** through **natural conversation**, powered by a **universal interface** and a **real-time event fabric**.

The system must be **conversation-first, event-driven, and universally accessible**, with **zero compromises** on security, scalability, or compliance.

#### **Mandatory Technology Stack**
*   **Backend Services**: `Node.js` with `TypeScript`, React, PostgreSQL, for high-performance, concurrent services (e.g., Router, Event Bus).
*   **Frontend**: `React` with `TypeScript`, `Tailwind CSS`, 'ShadeCN' `Storybook` for component development.
*   **Real-Time Communication**: `uWebSockets.js` for WebSocket server, `Redis` for Streams and Pub/Sub.
*   **Primary Database**: `PostgreSQL` with the `pgvector` extension.
*   **Caching & Session Store**: `Redis`.
*   **Secrets Management**: `Hashicorp Vault`.
*   **Containerization & Orchestration**: `Docker` and `Kubernetes`.
*   **UI Component Development**: `Storybook` with `Tailwind CSS`.
*   **Service Mesh & Security**: `Envoy Proxy` with `SPIRE` for SPIFFE identities and mTLS.
*   **Policy Engine**: `Open Policy Agent (OPA)` with `Rego` policies.
*   **CI/CD & Infrastructure**: `GitHub Actions` and `Terraform`.

> **This stack is non-negotiable.** The implementation plan must be based on these technologies.

---

### 📐 **Foundational Architecture (APIX + UAUIP Protocol)**

The entire platform is built on two core protocols:

#### 1. **APIX (AI Protocol Exchange): The Real-Time Nervous System**
*   **Transport**: `WebSocket` with `Server-Sent Events (SSE)` fallback for maximum compatibility.
*   **Latency**: Ensure **sub-100ms UI updates** across all modules.
*   **Events**: Implement the following **14+ real-time events** as the primary communication mechanism:
    *   `user_message`, `thinking_status`, `text_chunk`, `tool_call_start`, `tool_call_result`, `tool_call_error`
    *   `request_user_input`, `user_response`, `state_update`, `workflow_step`, `agent_handoff`
    *   `hybrid_execution`, `knowledge_retrieval`, `prompt_optimization`
    *   `auth_status_change`, `permission_update`, `org_switch`, `session_warning`
    *   `provider_health_update`, `routing_decision`, `cost_alert`
    *   `agent_communication`, `memory_update`, `task_delegation`
    *   `hybrid_context_update`, `document_processing_status`, `rag_context_injection`
*   **State Management**: Implement **server-managed state** with **delta sync** to clients. The server is the single source of truth.
*   **Event Persistence & Delivery**: Use **Redis Streams with Consumer Groups** to guarantee **ordered, replayable, and back-pressure-aware** message delivery for all critical workflows.

#### 2. **UAUIP (Universal Agent UI Interaction Protocol): The Universal Interface**
*   **Universal Components**: Build **24 identical, reusable UI components** that work **seamlessly** across web, mobile, embedded contexts (e.g., CRM, Slack), and API consumers.
*   **Adaptive Rendering**: Components must adapt to screen size, input method, and performance.
*   **Consistency**: The user experience must be **identical** on every platform.
*   **Mandatory Components**:
    *   `UniversalLoginForm`, `PermissionGate`, `OrgSwitcher`, `UserProfile`
    *   `ProviderDashboard`, `CostTracker`, `HealthMonitor`, `RoutingVisualizer`
    *   `AgentChat`, `AgentBuilder`, `AgentMonitor`, `CollaborationView`, `TaskManager`, `MultiTaskMonitor`, `AgentCollaborationView`, `CommunicationTimeline`, `CollaborationMonitor`
    *   `ToolMarketplace`, `ToolExecutor`, `ToolBuilder`, `ToolMonitor`
    *   `HybridMonitor`, `ToolSelectionView`, `ContextFlowVisualization`, `HybridPerformance`
    *   `WorkflowCanvas`, `WorkflowMonitor`, `WorkflowTemplates`, `WorkflowDebugger`
    *   `DocumentUploader`, `KnowledgeExplorer`, `RAGVisualization`, `KnowledgeAnalytics`
    *   `PromptEditor`, `TestingLab`, `VersionControl`, `PerformanceAnalytics`
*   **Open Standards**: Adopt **A2A (Agent-to-Agent)** and **MCP (Model Context Protocol)** open specifications for all agent and tool communication to enable a third-party ecosystem.

---

### 🗺️ **Phased Development Roadmap (Sequential & Dependency-Aware)**

*(The roadmap remains the same as in the previous version, but now it is understood to be built with the specified stack.)*

#### **Phase 0: APIX + UAUIP Protocol Foundation**
*   **Goal**: Establish the real-time communication layer and universal component framework.
*   **Tasks**:
    *   Set up a scalable WebSocket server using `uWebSockets.js` with SSE fallback.
    *   Implement **Redis Streams with Consumer Groups** for event persistence, replay, and back-pressure.
    *   Build the event bus to validate, route, and publish all 14+ `apixEvents`.
    *   Bootstrap the React-based universal component library with `Storybook`.
    *   Implement server-managed state with delta sync and multi-tab synchronization.
*   **Deliverable**: A working real-time event fabric and a complete library of 24 universal UI components.

#### **Phase 1: User Authentication & RBAC + Multi-Tenant**
*   **Goal**: Deliver a secure, scalable, and real-time authentication system.
*   **Tasks**:
    *   Integrate **Clerk** for authentication (email, social, magic links) and **SSO** (SAML, OIDC, LDAP).
    *   Implement **2FA/MFA** with TOTP and backup codes.
    *   Design and deploy a database schema in `PostgreSQL` for users, organizations, roles, and permissions.
    *   Enforce **multi-tenant isolation** via `organization_id` in all data models.
    *   Emit real-time `apixEvents`: `auth_status_change`, `permission_update`, `org_switch`, `session_warning`.
    *   Integrate UAUIP components: `UniversalLoginForm`, `PermissionGate`, `OrgSwitcher`, `UserProfile`.
*   **Deliverable**: An enterprise-grade authentication system with complete RBAC, multi-tenancy, and real-time state management.

#### **Phase 2: AI Provider Management System**
*   **Goal**: Enable intelligent, real-time routing across multiple AI providers.
*   **Tasks**:
    *   Integrate **real APIs** for OpenAI, Anthropic, Google, Groq, Mistral, and custom providers.
    *   Securely store and rotate API keys using **Vault**.
    *   Build a **routing engine** with strategies: cost, performance, quality, balanced.
    *   Implement **health monitoring** with automatic failover and circuit breakers.
    *   Calculate real-time costs using actual provider pricing.
    *   **Integrate dynamic provider keys from Vault** and a **24-hour rotation Lambda**.
    *   Emit `apixEvents`: `provider_health_update`, `routing_decision`, `cost_alert`, `performance_metric`, `failover_triggered`.
    *   Build UAUIP components: `ProviderDashboard`, `CostTracker`, `HealthMonitor`, `RoutingVisualizer`.
*   **Deliverable**: A production-ready provider management system with intelligent routing and real-time monitoring.

#### **Phase 3: Intelligent Agent System**
*   **Goal**: Build a robust agent system with multi-task, multi-provider, and collaborative capabilities.
*   **Tasks**:
    *   Implement agent types: `singleTask`, `multiTask`, `collaborative`, `supervisory`.
    *   Support **multi-provider intelligence** with dynamic switching.
    *   Build a **communication hub** for agent-to-agent messaging and context sharing.
    *   Implement **persistent memory**: short-term, long-term, episodic, semantic.
    *   **Crucially, implement the Multi-Task Agent and Agent-to-Agent enhancements**:
        *   Emit `apixEnhancement` events: `task_decomposition`, `multi_task_coordination`, `provider_switching`, `handoff_notification`, `collaboration_status`.
        *   Build `uauipEnhancement` components: `TaskManager`, `MultiTaskMonitor`, `TaskDecompositionView`, `AgentCollaborationView`, `CommunicationTimeline`, `CollaborationMonitor`.
    *   Emit `apixEvents`: `thinking_status`, `provider_switch`, `agent_communication`, `memory_update`, `task_delegation`, `learning_update`.
    *   Build UAUIP components: `AgentChat`, `AgentBuilder`, `AgentMonitor`, `CollaborationView`.
*   **Deliverable**: An enterprise-grade agent system with collaboration, memory, and persistent learning.

#### **Phase 4: Advanced Tool Integration**
*   **Goal**: Deliver a comprehensive tool ecosystem with real-time execution and marketplace.
*   **Tasks**:
    *   Integrate 100+ real tools (Slack, Salesforce, GitHub, Stripe, etc.) using OAuth and API keys.
    *   Build a **custom tool SDK** for external developers.
    *   Support sync, async, streaming, parallel, and batch execution with retry logic.
    *   Build a marketplace with search, ratings, and one-click installation.
    *   **Ensure all agent-to-agent and agent-to-tool communication uses the A2A/MCP open specs.**
    *   Emit `apixEvents`: `tool_call_start`, `tool_call_progress`, `tool_call_result`, `tool_call_error`, `tool_health_update`, `tool_installed`.
    *   Build UAUIP components: `ToolMarketplace`, `ToolExecutor`, `ToolBuilder`, `ToolMonitor`.
*   **Deliverable**: A comprehensive tool integration system with a real-time marketplace.

#### **Phase 5: Agent + Tool Hybrid System**
*   **Goal**: Enable seamless, intelligent collaboration between agents and tools.
*   **Tasks**:
    *   Implement dynamic tool selection based on task analysis.
    *   Preserve context across agent-tool transitions.
    *   Chain agent→tool→agent operations intelligently.
    *   Process and validate tool results.
    *   Optimize hybrid patterns via learning.
    *   Emit `apixEvents`: `hybrid_execution_start`, `tool_selection_reasoning`, `hybrid_context_update`, `tool_result_processing`, `hybrid_chain_step`, `hybrid_optimization`.
    *   Build UAUIP components: `HybridMonitor`, `ToolSelectionView`, `ContextFlowVisualization`, `HybridPerformance`.
*   **Deliverable**: A production-ready agent-tool hybrid system with real-time monitoring.

#### **Phase 6: Visual Workflow Builder React Flow**
*   **Goal**: Deliver a dual-mode workflow engine with real-time execution and debugging.
*   **Tasks**:
    *   Build a **drag-and-drop** interface using React Flow and a **form-based** creation interface (as the primary mode).
    *   Support node types: agent, tool, condition, loop, humanApproval, parallel, delay, webhook.
    *   Enable real-time execution, debugging, and scheduling.
    *   Add versioning and rollback.
    *   Emit `apixEvents`: `workflow_start`, `workflow_step`, `workflow_node_active`, `workflow_error`, `workflow_optimization`, `workflow_complete`.
    *   Build UAUIP components: `WorkflowCanvas`, `WorkflowMonitor`, `WorkflowTemplates`, `WorkflowDebugger`.
*   **Deliverable**: An enterprise-grade workflow builder with real-time execution.

#### **Phase 7: Knowledge Base & RAG System**
*   **Goal**: Build a production-grade RAG system with real-time indexing and agent enhancement.
*   **Tasks**:
    *   Ingest PDF, DOCX, TXT, CSV, HTML, Markdown, and images (with OCR).
    *   Implement intelligent chunking and generate embeddings using multiple models.
    *   Index in **PostgreSQL with pgvector** for semantic search.
    *   Inject retrieved knowledge into agent prompts in real time.
    *   Emit `apixEvents`: `document_processing_status`, `knowledge_retrieval`, `rag_context_injection`, `embedding_generation`, `knowledge_update`, `search_optimization`.
    *   Build UAUIP components: `DocumentUploader`, `KnowledgeExplorer`, `RAGVisualization`, `KnowledgeAnalytics`.
*   **Deliverable**: A production-ready knowledge base with advanced RAG capabilities.

#### **Phase 8: Prompt Management & Template System**
*   **Goal**: Deliver a Git-like prompt management system with real testing and optimization.
*   **Tasks**:
    *   Build an advanced editor with syntax highlighting and variable injection.
    *   Implement **Git-like versioning** with branching, merging, diff, and rollback.
    *   Add **A/B testing** with statistical analysis using real AI responses.
    *   Add provider-specific optimization and AI suggestions.
    *   **Instrument an AWS Budgets anomaly detector to trigger automatic cost-saving actions in the `CostOptimizer`.**
    *   Emit `apixEvents`: `prompt_testing`, `performance_metrics`, `optimization_suggestions`, `version_comparison`, `ab_test_results`, `quality_score_update`.
    *   Build UAUIP components: `PromptEditor`, `TestingLab`, `VersionControl`, `PerformanceAnalytics`.
*   **Deliverable**: An enterprise-grade prompt management system.

---

### 🔐 **Security & Compliance Requirements (Non-Negotiable)**

*   **Authentication**: JWT with Clerk, 2FA, Redis-backed sessions with CSRF protection.
*   **Authorization**: **Fine-grained RBAC with OPA/Rego policies, enforced by Envoy sidecars using the `ext_authz` filter.**
*   **Data Security**: TLS 1.3, AES-256 encryption at rest, secrets in Vault/KMS.
*   **Data Isolation**: Row-Level Security (RLS) by `organization_id` for multi-tenancy.
*   **Network Security**: **All inter-service and agent traffic uses SPIFFE identities and mTLS.**
*   **Audit Trails**: Immutable, append-only logs in a system like QLDB or PostgreSQL with pgAudit. Each event must be hashed and signed.
*   **Compliance**: Design for SOC 2, GDPR, and CCPA.

---

### 🏁 **Final Output Requirements**

Generate a **detailed, production-ready implementation plan** that includes:
1.  **A Phased Development Roadmap**: A markdown-formatted document with 8 sequential phases, each detailing core functionality, APIX integration, UAUIP implementation, technical integration, critical rules compliance, and a deliverable.
2.  **Architecture Diagrams**: High-level diagrams for the APIX event flow, UAUIP component structure, and overall system architecture.
3.  **API Contracts**: OpenAPI specifications for key backend services (e.g., RouterEngine, CoordinationBroker).
4.  **UI Wireframes**: Mockups for the key universal components.
5.  **Deployment Strategy**: A plan for deploying on Kubernetes with Helm charts, including scaling, monitoring (Prometheus/Grafana), and CI/CD pipelines.
6.  **Verification Checklist**: A list of critical rules for each module that must be verified before release.

> **This is a production system. Every integration must be real, every security rule must be enforced, and every component must be universal. There are no mocks.**